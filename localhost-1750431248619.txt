export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 6/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 7/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 8/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 9/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
(index):3380 Detected export response format, extracting data: {exported_at: '2025-06-21T00:03:27.493565', name: 'AU-PROD-IP14', variables: Array(24), version: '1.0'}
(index):3384 Final dataToImport structure: {exported_at: '2025-06-21T00:03:27.493565', name: 'AU-PROD-IP14', variables: Array(24), version: '1.0'}
(index):3385 Has environments array? undefined
(index):3386 Has name field? AU-PROD-IP14
(index):3387 Variables count: 24
(index):3414 Importing single environment: {exported_at: '2025-06-21T00:03:27.493565', name: 'AU-PROD-IP14', variables: Array(24), version: '1.0'}
(index):3415 Environment name: AU-PROD-IP14
(index):3416 Variables to import: (24) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
(index):3417 First few variables: (3) [{…}, {…}, {…}]
(index):3420 Single import result: {success: true, data: {…}, status: 201}
(index):2672 Current environment response: {success: true, data: {…}, status: 200}
(index):2804 Selecting environment UI: 18 Current active: null
(index):2893 Variables API response: {success: true, data: Array(24), status: 200}
(index):2896 Variables data: (24) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: 'au.com.kmart', id: 286, initial_value: 'au.com.kmart', name: 'appid', …}
(index):2929 Rendering variable row for: appid initial_value: au.com.kmart current_value: au.com.kmart
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: 'add-to-bag-ip14.png', id: 287, initial_value: 'add-to-bag-ip14.png', name: 'atg-pdp', …}
(index):2929 Rendering variable row for: atg-pdp initial_value: add-to-bag-ip14.png current_value: add-to-bag-ip14.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: 'catalogue-menu.png', id: 288, initial_value: 'catalogue-menu.png', name: 'catalogue-menu-img', …}
(index):2929 Rendering variable row for: catalogue-menu-img initial_value: catalogue-menu.png current_value: catalogue-menu.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: 'banner-close-updated.png', id: 289, initial_value: 'banner-close-updated.png', name: 'closebtnimage', …}
(index):2929 Rendering variable row for: closebtnimage initial_value: banner-close-updated.png current_value: banner-close-updated.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: 'P_43375509', id: 290, initial_value: 'P_43375509', name: 'cooker-id', …}
(index):2929 Rendering variable row for: cooker-id initial_value: P_43375509 current_value: P_43375509
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: '305 238 Flinders', id: 291, initial_value: '305 238 Flinders', name: 'deliver-address', …}
(index):2929 Rendering variable row for: deliver-address initial_value: 305 238 Flinders current_value: 305 238 Flinders
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: '54', id: 292, initial_value: '54', name: 'delivery-addr-x', …}
(index):2929 Rendering variable row for: delivery-addr-x initial_value: 54 current_value: 54
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '314', id: 293, initial_value: '314', name: 'delivery-addr-y', …}
(index):2929 Rendering variable row for: delivery-addr-y initial_value: 314 current_value: 314
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'delivery-address-ip14.png', id: 294, initial_value: 'delivery-address-ip14.png', name: 'delivery-address-img', …}
(index):2929 Rendering variable row for: delivery-address-img initial_value: delivery-address-ip14.png current_value: delivery-address-ip14.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'device-back-ip14.png', id: 295, initial_value: 'device-back-ip14.png', name: 'device-back-img', …}
(index):2929 Rendering variable row for: device-back-img initial_value: device-back-ip14.png current_value: device-back-ip14.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'homepage_editbtn-se.png', id: 296, initial_value: 'homepage_editbtn-se.png', name: 'homepage-edit-link-img', …}
(index):2929 Rendering variable row for: homepage-edit-link-img initial_value: homepage_editbtn-se.png current_value: homepage_editbtn-se.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'paypalclose-ip14.png', id: 297, initial_value: 'paypalclose-ip14.png', name: 'paypal-close-img', …}
(index):2929 Rendering variable row for: paypal-close-img initial_value: paypalclose-ip14.png current_value: paypalclose-ip14.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'product-share-ip14.png', id: 298, initial_value: 'product-share-ip14.png', name: 'product-share-img', …}
(index):2929 Rendering variable row for: product-share-img initial_value: product-share-ip14.png current_value: product-share-ip14.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'Wonderbaby@5', id: 299, initial_value: 'Wonderbaby@5', name: 'pwd', …}
(index):2929 Rendering variable row for: pwd initial_value: Wonderbaby@5 current_value: Wonderbaby@5
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'Wonderbaby@6', id: 300, initial_value: 'Wonderbaby@6', name: 'pwd-op', …}
(index):2929 Rendering variable row for: pwd-op initial_value: Wonderbaby@6 current_value: Wonderbaby@6
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '447743749', id: 301, initial_value: '447743749', name: 'searchorder', …}
(index):2929 Rendering variable row for: searchorder initial_value: 447743749 current_value: 447743749
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '3000', id: 302, initial_value: '3000', name: 'store-locator-postcode', …}
(index):2929 Rendering variable row for: store-locator-postcode initial_value: 3000 current_value: 3000
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '24', id: 303, initial_value: '24', name: 'store-locator-x', …}
(index):2929 Rendering variable row for: store-locator-x initial_value: 24 current_value: 24
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '296', id: 304, initial_value: '296', name: 'store-locator-y', …}
(index):2929 Rendering variable row for: store-locator-y initial_value: 296 current_value: 296
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'com.apple.TestFlight', id: 305, initial_value: 'com.apple.TestFlight', name: 'tfappid', …}
(index):2929 Rendering variable row for: tfappid initial_value: com.apple.TestFlight current_value: com.apple.TestFlight
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '<EMAIL>', id: 306, initial_value: '<EMAIL>', name: 'uname', …}
(index):2929 Rendering variable row for: uname initial_value: <EMAIL> current_value: <EMAIL>
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '<EMAIL>', id: 307, initial_value: '<EMAIL>', name: 'uname-op', …}
(index):2929 Rendering variable row for: uname-op initial_value: <EMAIL> current_value: <EMAIL>
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '<EMAIL>', id: 308, initial_value: '<EMAIL>', name: 'uname1', …}
(index):2929 Rendering variable row for: uname1 initial_value: <EMAIL> current_value: <EMAIL>
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'Uno card', id: 309, initial_value: 'Uno card', name: 'uno', …}
(index):2929 Rendering variable row for: uno initial_value: Uno card current_value: Uno card
(index):2719 Populating selectors with active environment: null
(index):2804 Selecting environment UI: 18 Current active: null
(index):2893 Variables API response: {success: true, data: Array(24), status: 200}
(index):2896 Variables data: (24) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: 'au.com.kmart', id: 286, initial_value: 'au.com.kmart', name: 'appid', …}
(index):2929 Rendering variable row for: appid initial_value: au.com.kmart current_value: au.com.kmart
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: 'add-to-bag-ip14.png', id: 287, initial_value: 'add-to-bag-ip14.png', name: 'atg-pdp', …}
(index):2929 Rendering variable row for: atg-pdp initial_value: add-to-bag-ip14.png current_value: add-to-bag-ip14.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: 'catalogue-menu.png', id: 288, initial_value: 'catalogue-menu.png', name: 'catalogue-menu-img', …}
(index):2929 Rendering variable row for: catalogue-menu-img initial_value: catalogue-menu.png current_value: catalogue-menu.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: 'banner-close-updated.png', id: 289, initial_value: 'banner-close-updated.png', name: 'closebtnimage', …}
(index):2929 Rendering variable row for: closebtnimage initial_value: banner-close-updated.png current_value: banner-close-updated.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: 'P_43375509', id: 290, initial_value: 'P_43375509', name: 'cooker-id', …}
(index):2929 Rendering variable row for: cooker-id initial_value: P_43375509 current_value: P_43375509
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: '305 238 Flinders', id: 291, initial_value: '305 238 Flinders', name: 'deliver-address', …}
(index):2929 Rendering variable row for: deliver-address initial_value: 305 238 Flinders current_value: 305 238 Flinders
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:01', current_value: '54', id: 292, initial_value: '54', name: 'delivery-addr-x', …}
(index):2929 Rendering variable row for: delivery-addr-x initial_value: 54 current_value: 54
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '314', id: 293, initial_value: '314', name: 'delivery-addr-y', …}
(index):2929 Rendering variable row for: delivery-addr-y initial_value: 314 current_value: 314
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'delivery-address-ip14.png', id: 294, initial_value: 'delivery-address-ip14.png', name: 'delivery-address-img', …}
(index):2929 Rendering variable row for: delivery-address-img initial_value: delivery-address-ip14.png current_value: delivery-address-ip14.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'device-back-ip14.png', id: 295, initial_value: 'device-back-ip14.png', name: 'device-back-img', …}
(index):2929 Rendering variable row for: device-back-img initial_value: device-back-ip14.png current_value: device-back-ip14.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'homepage_editbtn-se.png', id: 296, initial_value: 'homepage_editbtn-se.png', name: 'homepage-edit-link-img', …}
(index):2929 Rendering variable row for: homepage-edit-link-img initial_value: homepage_editbtn-se.png current_value: homepage_editbtn-se.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'paypalclose-ip14.png', id: 297, initial_value: 'paypalclose-ip14.png', name: 'paypal-close-img', …}
(index):2929 Rendering variable row for: paypal-close-img initial_value: paypalclose-ip14.png current_value: paypalclose-ip14.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'product-share-ip14.png', id: 298, initial_value: 'product-share-ip14.png', name: 'product-share-img', …}
(index):2929 Rendering variable row for: product-share-img initial_value: product-share-ip14.png current_value: product-share-ip14.png
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'Wonderbaby@5', id: 299, initial_value: 'Wonderbaby@5', name: 'pwd', …}
(index):2929 Rendering variable row for: pwd initial_value: Wonderbaby@5 current_value: Wonderbaby@5
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'Wonderbaby@6', id: 300, initial_value: 'Wonderbaby@6', name: 'pwd-op', …}
(index):2929 Rendering variable row for: pwd-op initial_value: Wonderbaby@6 current_value: Wonderbaby@6
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '447743749', id: 301, initial_value: '447743749', name: 'searchorder', …}
(index):2929 Rendering variable row for: searchorder initial_value: 447743749 current_value: 447743749
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '3000', id: 302, initial_value: '3000', name: 'store-locator-postcode', …}
(index):2929 Rendering variable row for: store-locator-postcode initial_value: 3000 current_value: 3000
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '24', id: 303, initial_value: '24', name: 'store-locator-x', …}
(index):2929 Rendering variable row for: store-locator-x initial_value: 24 current_value: 24
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '296', id: 304, initial_value: '296', name: 'store-locator-y', …}
(index):2929 Rendering variable row for: store-locator-y initial_value: 296 current_value: 296
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'com.apple.TestFlight', id: 305, initial_value: 'com.apple.TestFlight', name: 'tfappid', …}
(index):2929 Rendering variable row for: tfappid initial_value: com.apple.TestFlight current_value: com.apple.TestFlight
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '<EMAIL>', id: 306, initial_value: '<EMAIL>', name: 'uname', …}
(index):2929 Rendering variable row for: uname initial_value: <EMAIL> current_value: <EMAIL>
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '<EMAIL>', id: 307, initial_value: '<EMAIL>', name: 'uname-op', …}
(index):2929 Rendering variable row for: uname-op initial_value: <EMAIL> current_value: <EMAIL>
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: '<EMAIL>', id: 308, initial_value: '<EMAIL>', name: 'uname1', …}
(index):2929 Rendering variable row for: uname1 initial_value: <EMAIL> current_value: <EMAIL>
(index):2901 Rendering variable: {created_at: '2025-06-20 14:52:02', current_value: 'Uno card', id: 309, initial_value: 'Uno card', name: 'uno', …}
(index):2929 Rendering variable row for: uno initial_value: Uno card current_value: Uno card
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 10/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
(index):2652 Saved active environment to session storage: 18
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 11/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 12/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250620_225143
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250620_225143
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250620_225143
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250620_225143/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250620_225143
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250620_225143
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250620_225143
   - isExecuting: false
   - latestReportId: testsuite_execution_20250620_225143
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
