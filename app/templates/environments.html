<div class="container-fluid mt-3">
    <div class="row mb-3">
        <div class="col-md-auto">
            <h4>Selected Environment</h4>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="selectedEnvironment">
                <option value="">Select Environment</option>
            </select>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-primary" id="saveEnvironment">Save</button>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-success" id="createNewEnvironmentBtn"><i class="bi bi-plus-circle"></i> Create New</button>
        </div>
    </div>

    <div class="row">
        <!-- Left column for environment list -->
        <div class="col-md-3">
            <div class="list-group" id="environmentList">
                <div class="text-muted p-2">Loading environments...</div>
            </div>
        </div>

        <!-- Right column for environment variables -->
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h5 id="environmentNameDisplay">Select an environment</h5>
            </div>
            <div class="input-group mb-3">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" placeholder="Filter variables" id="filterVariables">
            </div>
            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th scope="col" style="width: 5%;"><input class="form-check-input" type="checkbox" value="" id="selectAllVariables"></th>
                        <th scope="col">Variable</th>
                        <th scope="col">Initial value</th>
                        <th scope="col">Current value</th>
                        <th scope="col" style="width: 10%;">Actions</th>
                    </tr>
                </thead>
                <tbody id="environmentVariablesTableBody">
                    <tr>
                        <td colspan="5" class="text-center">Select an environment to view variables</td>
                    </tr>
                </tbody>
            </table>
            <button class="btn btn-outline-primary" id="addNewVariable"><i class="bi bi-plus-lg"></i> Add new variable</button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function () {
    console.log('🚀 Environment page loaded');

    // Get DOM elements
    const selectedEnvironmentDropdown = document.getElementById('selectedEnvironment');
    const environmentNameDisplay = document.getElementById('environmentNameDisplay');
    const environmentVariablesTableBody = document.getElementById('environmentVariablesTableBody');
    const environmentList = document.getElementById('environmentList');
    const addNewVariableButton = document.getElementById('addNewVariable');
    const createNewEnvironmentButton = document.getElementById('createNewEnvironmentBtn');
    const saveEnvironmentBtn = document.getElementById('saveEnvironment');

    let currentSelectedEnvId = null;
    let environmentsData = [];
    let variablesData = [];

    // Simple API call function
    async function apiCall(url, method = 'GET', data = null) {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            if (data) {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(url, options);
            const result = await response.json();

            if (!response.ok) {
                console.error('API Error:', result);
                alert('Error: ' + (result.error || 'Unknown error'));
                return null;
            }

            return result;
        } catch (error) {
            console.error('Network Error:', error);
            alert('Network error: ' + error.message);
            return null;
        }
    }

    // Load environments
    async function loadEnvironments() {
        console.log('🔄 Loading environments...');
        const environments = await apiCall('/api/environments');
        if (!environments) return;

        environmentsData = environments;
        console.log('✅ Loaded environments:', environments);

        // Populate dropdown
        selectedEnvironmentDropdown.innerHTML = '<option value="">Select Environment</option>';
        environments.forEach(env => {
            const option = document.createElement('option');
            option.value = env.id;
            option.textContent = env.name;
            selectedEnvironmentDropdown.appendChild(option);
        });

        // Populate environment list
        environmentList.innerHTML = '';
        environments.forEach(env => {
            const item = document.createElement('a');
            item.href = '#';
            item.className = 'list-group-item list-group-item-action';
            item.textContent = env.name;
            item.onclick = (e) => {
                e.preventDefault();
                selectEnvironment(env.id);
            };
            environmentList.appendChild(item);
        });

        // Auto-select first environment
        if (environments.length > 0) {
            selectEnvironment(environments[0].id);
        }
    }

    // Select environment
    async function selectEnvironment(envId) {
        console.log('🎯 Selecting environment:', envId);
        currentSelectedEnvId = envId;

        const env = environmentsData.find(e => e.id == envId);
        if (!env) return;

        // Update UI
        environmentNameDisplay.textContent = env.name;
        selectedEnvironmentDropdown.value = envId;

        // Update environment list active state
        environmentList.querySelectorAll('.list-group-item').forEach(item => {
            item.classList.remove('active');
        });
        environmentList.querySelector(`[onclick*="${envId}"]`)?.classList.add('active');

        // Load variables
        await loadVariables(envId);
    }

    // Load variables for environment
    async function loadVariables(envId) {
        console.log('🔄 Loading variables for environment:', envId);
        const variables = await apiCall(`/api/environments/${envId}/variables`);
        if (!variables) return;

        variablesData = variables;
        console.log('✅ Loaded variables:', variables.length);

        // Clear table
        environmentVariablesTableBody.innerHTML = '';

        if (variables.length === 0) {
            environmentVariablesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">No variables. Click "Add new variable" to add one.</td></tr>';
            return;
        }

        // Add variables to table
        variables.forEach(variable => {
            const row = environmentVariablesTableBody.insertRow();
            row.innerHTML = `
                <td><input class="form-check-input" type="checkbox"></td>
                <td>${variable.name}</td>
                <td>${variable.initial_value || ''}</td>
                <td>${variable.current_value || ''}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editVariable(${variable.id})">Edit</button>
                    <button class="btn btn-sm btn-outline-danger ms-1" onclick="deleteVariable(${variable.id})">Delete</button>
                </td>
            `;
        });
    }

    // Add new variable
    async function addNewVariable() {
        if (!currentSelectedEnvId) {
            alert('Please select an environment first');
            return;
        }

        const name = prompt('Variable name:');
        if (!name) return;

        const initialValue = prompt('Initial value:') || '';
        const currentValue = prompt('Current value:') || initialValue;

        const result = await apiCall(`/api/environments/${currentSelectedEnvId}/variables`, 'POST', {
            name: name,
            type: 'default',
            initial_value: initialValue,
            current_value: currentValue
        });

        if (result) {
            alert('Variable added successfully!');
            await loadVariables(currentSelectedEnvId);
        }
    }

    // Edit variable (simple version)
    window.editVariable = async function(varId) {
        const variable = variablesData.find(v => v.id == varId);
        if (!variable) return;

        const name = prompt('Variable name:', variable.name);
        if (!name) return;

        const initialValue = prompt('Initial value:', variable.initial_value) || '';
        const currentValue = prompt('Current value:', variable.current_value) || '';

        const result = await apiCall(`/api/environment_variables/${varId}`, 'PUT', {
            name: name,
            type: variable.type || 'default',
            initial_value: initialValue,
            current_value: currentValue
        });

        if (result) {
            alert('Variable updated successfully!');
            await loadVariables(currentSelectedEnvId);
        }
    }

    // Delete variable
    window.deleteVariable = async function(varId) {
        if (!confirm('Are you sure you want to delete this variable?')) return;

        const result = await apiCall(`/api/environment_variables/${varId}`, 'DELETE');
        if (result) {
            alert('Variable deleted successfully!');
            await loadVariables(currentSelectedEnvId);
        }
    }

    // Event listeners
    selectedEnvironmentDropdown.addEventListener('change', function() {
        if (this.value) {
            selectEnvironment(this.value);
        }
    });

    addNewVariableButton.addEventListener('click', addNewVariable);

    createNewEnvironmentButton.addEventListener('click', async function() {
        const name = prompt('Environment name:');
        if (!name) return;

        const result = await apiCall('/api/environments', 'POST', { name: name });
        if (result) {
            alert('Environment created successfully!');
            await loadEnvironments();
        }
    });

    saveEnvironmentBtn.addEventListener('click', async function() {
        if (!currentSelectedEnvId) {
            alert('Please select an environment first');
            return;
        }

        const result = await apiCall('/api/environments/current', 'POST', {
            environment_id: currentSelectedEnvId
        });

        if (result) {
            alert('Environment set as active!');
        }
    });

    // Initialize
    loadEnvironments();
});
</script>